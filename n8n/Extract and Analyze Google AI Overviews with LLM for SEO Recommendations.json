{"name": "Extract and Analyze Google AI Overviews with LLM for SEO Recommendations", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-overview-extractor-template-123456789", "responseMode": "lastNode", "options": {}}, "id": "d54405a8-ce00-4f39-bcea-fae97338a27f", "name": "Webhook", "type": "n8n-nodes-base.webhook", "position": [448, 368], "webhookId": "81d02aee-0018-48f4-b228-0716bba9bf8b", "typeVersion": 2}, {"parameters": {}, "id": "2853ad27-91c9-405a-b696-b077823a1021", "name": "When clicking 'Execute workflow'", "type": "n8n-nodes-base.manualTrigger", "position": [480, 1088], "typeVersion": 1}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/15xqZ2dTiLMoyICYnnnRV-HPvXfdgVeXowr8a7kU4uHk/edit?gid=0#gid=0"}, "sheetName": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/15xqZ2dTiLMoyICYnnnRV-HPvXfdgVeXowr8a7kU4uHk/edit?gid=0#gid=0"}, "columns": {"value": {"key": "={{ $json.searchQuery }} {{ $json.extractedAt }}", "task": "create guidelines", "sources": "={{ \"\" || $json.sources.map(item => `* ${item.title} - ${item.url.extractDomain()} - ${item.url}`).join('\\n') }}", "markdown": "={{ $json.markdown }}", "extractedAt": "={{ $json.extractedAt }}", "searchQuery": "={{ $json.searchQuery }}"}, "schema": [{"id": "extractedAt", "type": "string", "display": true, "removed": false, "required": false, "displayName": "extractedAt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "searchQuery", "type": "string", "display": true, "removed": false, "required": false, "displayName": "searchQuery", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sources", "type": "string", "display": true, "removed": false, "required": false, "displayName": "sources", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "markdown", "type": "string", "display": true, "removed": false, "required": false, "displayName": "markdown", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "myURL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "myURL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "task", "type": "string", "display": true, "removed": false, "required": false, "displayName": "task", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "guidelines", "type": "string", "display": true, "removed": true, "required": false, "displayName": "guidelines", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "removed": false, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["key"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {"cellFormat": "RAW"}}, "id": "9dfbb1ee-6979-49bd-8963-f1b7f339feb5", "name": "Save AI Overview to Sheets", "type": "n8n-nodes-base.googleSheets", "position": [1200, 368], "notesInFlow": true, "typeVersion": 4.6, "notes": "Send all data to Sheets"}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "feb9922a-de0c-41d8-93d7-1f92ee7398b2", "operator": {"type": "string", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.myURL }}", "rightValue": ""}]}, "options": {}}, "id": "b2453757-c908-4747-8029-2d24e5690be4", "name": "myURL Exists", "type": "n8n-nodes-base.filter", "position": [1136, 1184], "notesInFlow": true, "typeVersion": 2.2, "notes": "Only records with myURL"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "title", "cssSelector": "title"}, {"key": "h1", "cssSelector": "h1"}]}, "options": {}}, "id": "f48fc7a2-c6ac-4426-9986-e987e5dc9a23", "name": "Extract Page Title and H1", "type": "n8n-nodes-base.html", "position": [2016, 1152], "notesInFlow": true, "typeVersion": 1.2, "notes": "Extract meta title and h1"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "id": "d250bdc4-0b58-41f4-afa2-ba82f67d67d6", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [2240, 1040], "typeVersion": 3.2, "notes": "merge variables by position"}, {"parameters": {"url": "={{ $json.myURL }}", "options": {}}, "id": "67d57eb8-2edc-4592-a9e2-0a5c0bade571", "name": "Fetch Page Content", "type": "n8n-nodes-base.httpRequest", "position": [1792, 1040], "notesInFlow": true, "typeVersion": 4.2, "notes": "Fetch site"}, {"parameters": {"options": {}}, "id": "8d816cf1-dae5-4a47-9117-ed13dc9a4590", "name": "Process URLs in Batches", "type": "n8n-nodes-base.splitInBatches", "position": [1360, 1184], "typeVersion": 3}, {"parameters": {"includeOtherFields": true, "options": {}}, "id": "d8db7f48-21e3-4f7b-a48a-6931cf397601", "name": "Get Current URL Data", "type": "n8n-nodes-base.set", "position": [1568, 1040], "notesInFlow": true, "typeVersion": 3.4, "notes": "Single item data from loop"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/15xqZ2dTiLMoyICYnnnRV-HPvXfdgVeXowr8a7kU4uHk/edit?gid=0#gid=0"}, "sheetName": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/15xqZ2dTiLMoyICYnnnRV-HPvXfdgVeXowr8a7kU4uHk/edit?gid=0#gid=0"}, "columns": {"value": {"task": "guidelines created", "guidelines": "={{ $json.text }}", "row_number": "={{ $('Get Current URL Data').item.json.row_number }}"}, "schema": [{"id": "extractedAt", "type": "string", "display": true, "removed": true, "required": false, "displayName": "extractedAt", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "searchQuery", "type": "string", "display": true, "removed": true, "required": false, "displayName": "searchQuery", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "sources", "type": "string", "display": true, "removed": true, "required": false, "displayName": "sources", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "markdown", "type": "string", "display": true, "removed": true, "required": false, "displayName": "markdown", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "myURL", "type": "string", "display": true, "removed": true, "required": false, "displayName": "myURL", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "task", "type": "string", "display": true, "removed": false, "required": false, "displayName": "task", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "guidelines", "type": "string", "display": true, "removed": false, "required": false, "displayName": "guidelines", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "key", "type": "string", "display": true, "removed": true, "required": false, "displayName": "key", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "row_number", "type": "string", "display": true, "removed": false, "readOnly": true, "required": false, "displayName": "row_number", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["row_number"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "c82ae7fe-8369-461c-8661-ebd51371138b", "name": "Save SEO Guidelines to Sheets", "type": "n8n-nodes-base.googleSheets", "position": [2832, 1040], "notesInFlow": true, "typeVersion": 4.6, "notes": "send guidelines to sheets"}, {"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "id": "ecd9fbfa-328f-4507-a662-0c0d6e6f620e", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [480, 1280], "typeVersion": 1.2}, {"parameters": {}, "id": "90c30cd2-9b88-48e0-8846-065340014f29", "name": "Wait", "type": "n8n-nodes-base.wait", "position": [3056, 1184], "webhookId": "03dd000b-26dc-4905-8efb-19a7175e689e", "typeVersion": 1.1}, {"parameters": {"maxItems": 10}, "id": "e0516ed5-69cd-4543-8816-aa70579aef2a", "name": "Limit", "type": "n8n-nodes-base.limit", "position": [912, 1184], "notesInFlow": true, "typeVersion": 1, "notes": "Limit to 10 items"}, {"parameters": {"documentId": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/15xqZ2dTiLMoyICYnnnRV-HPvXfdgVeXowr8a7kU4uHk/edit?gid=0#gid=0"}, "sheetName": {"__rl": true, "mode": "url", "value": "https://docs.google.com/spreadsheets/d/15xqZ2dTiLMoyICYnnnRV-HPvXfdgVeXowr8a7kU4uHk/edit?gid=0#gid=0"}, "filtersUI": {"values": [{"lookupColumn": "task", "lookupValue": "create guidelines"}, {"lookupColumn": "guidelines"}]}, "options": {}}, "id": "30c3e6fb-044b-4d8c-8da2-5e67d95f397b", "name": "Get URLs to Analyze", "type": "n8n-nodes-base.googleSheets", "position": [720, 1152], "notesInFlow": true, "typeVersion": 4.6, "notes": "Get rows with task : \"create guidelines\" and without guidelines"}, {"parameters": {"model": "google/gemini-2.5-pro-preview", "options": {"responseFormat": "text"}}, "id": "2253258b-846f-445d-baa8-7cb754a32738", "name": "Gemini 2.5 Pro Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "position": [2448, 1264], "notesInFlow": true, "typeVersion": 1, "notes": "You can change model or node"}, {"parameters": {"html": "={{ $json.htmlContent }}", "destinationKey": "markdown", "options": {}}, "id": "5eeb0cd6-7fd9-4fd0-9053-08607bdc824c", "name": "Convert AI Overview to Markdown", "type": "n8n-nodes-base.markdown", "position": [896, 368], "notesInFlow": true, "typeVersion": 1, "notes": "convert HTML to Markdown"}, {"parameters": {"content": "## Get data\nThis flow get data from webhook and stores it into Google Sheets. After you get data in sheets you need paste your site url in myURL and wait for guidelines.", "height": 320, "width": 1060}, "id": "85e2d470-f80a-44db-8166-31b2883a310b", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [368, 240], "typeVersion": 1}, {"parameters": {"promptType": "define", "text": "=# KEYWORD:\n{{ $('Get Current URL Data').item.json.searchQuery }}\n\n# PAGE CONTENT:\n{{ $json.data }}\n\n# AI OVERVIEW CONTENT:\n{{ $('Get Current URL Data').item.json.markdown }}\n\n# Task\nDescribe exactly what I need to change in my page content, and specify where to add or modify things. Provide clear, actionable instructions so I can implement the changes quickly.\n", "messages": {"messageValues": [{"message": "=Your name is <PERSON>, you are a helpful assistant.\n\nThe user will provide you with:\n- KEYWORD\n- PAGE CONTENT\n- AI OVERVIEWS (Google's Overview)\n\n<PERSON> is an expert on AI Overviews (AI Overview) results and adapting content to meet AI Overviews (AI Overview) requirements.\n\n<PERSON>'s task is to prepare the content that should be placed on the page. This content should be what is missing from the PAGE CONTENT but is present in the AI OVERVIEWS.\n\n<PERSON> responds directly in Markdown format and provides only the answer with the information about what should be included, starting directly with the content.\n\nWhen suggesting changes or additions:\n- Focus on identifying missing information or areas for improvement in the PAGE CONTENT based on AI OVERVIEWS.\n- Provide concise, actionable instructions. For example:\n    - \"Consider adding a brief mention of [specific topic from AI Overview] in the section discussing [relevant part of PAGE CONTENT].\"\n    - \"The AI Overview highlights [key fact/detail]. You could integrate this into the paragraph starting with '...' by adding a sentence like: '[brief example sentence incorporating the fact/detail]'.\"\n    - \"To cover [new tool/concept from AI Overview], consider creating a new subsection under [existing heading] titled '[suggested subsection title]'. This subsection should briefly outline its [key feature 1] and [key feature 2].\"\n- Instead of providing fully drafted new sections or long paragraphs, suggest the key points, topics, or specific short phrases/sentences to incorporate.\n- If existing text needs modification, clearly state what needs to change and provide a concise suggestion. For example:\n  \"In the sentence: `The old sentence was like this.`, consider rephrasing to: `The new, improved sentence could be like this.` to better reflect [reason based on AI Overview].\"\n- The goal is to guide the user to make targeted updates quickly, not to rewrite large portions of their content for them. Emphasize specific, small changes.\n\nAlice does not use ``` code blocks when starting the response.\n\n<writing_guidelines>\n- Alice write regular hyphens (-) instead of en dashes (–) or em dashes (—) .\n- Alice begins with the most crucial and relevant information that directly addresses the problem.\n- Alice is specific and create useful text. Alice provides specific, useful, and actionable information without superfluous content.\n- Clarity and precision: be certain and specific. Cut the fluff. Bold important terms, definitions, and their entire explanations. For example: **No contact: This refers to completely cutting off communication with an ex-partner as a crucial step in the emotional healing process.**\n- Inclusive language: include diverse perspectives and inclusive language.\n- No summarization: do not include summarization or recapitulation.\n- Readability: ensure the text is highly readable, using short sentences, simple words, and clear, concise language.\n- Grammar and style: avoid grammatical and stylistic errors. Ensure proper punctuation and sentence structure.\n- MARKDOWN formatting: ensure all tags are properly used and closed. Do not use class or id attributes.\n- Consistency: maintain consistency in terminology and style throughout the content.\n- Expertise: demonstrate expertise and authority in the subject matter. Use reliable sources and evidence to support statements.\n- Engagement: write in an engaging manner to keep the reader interested. Use storytelling, analogies, and examples where appropriate.\n- Avoid creating subheadings\n- To create inline code, wrap with backticks ` .\n- To create a code block, either indent each line by 4 spaces, or place 3 backticks ``` on a line above and below the code block.\n</writing_guidelines>\n\n<language>\n- Use concise, clear wording.\n- Start questions with \"what,\" \"how,\" and \"should\" (but avoid overusing \"How to\").\n- Incorporate action verbs (e.g., improves, increases, prevents).\n- Focus on benefits, risks, and factual information.\n- Use specific terms related to the topic.\n- Avoid generic terms like \"Introduction\", \"Overview\", \"Complete\", \"Ultimate\", \"Definitive\".\n- Use phrases like \"What is it?\" or \"What is…\" instead of writing \"Definition\".\n- Don't use phrases like \"everything you need to know\" or \"a deep dive into\".\n- Skip words like \"essential\", \"crucial\", \"vital\".\n- Omit \"In Today's World\" or \"Modern Approach to\".\n- Avoid buzzwords/jargon unless necessary.\n- Don't start with \"understanding\" or \"exploring\".\n- Exclude filler words like \"really\", \"actually\", \"basically\".\n- Limit use of gerunds (e.g., \"understanding\", \"implementing\", \"analyzing\").\n- Don't use capital letter after \":\" sign.\n- Use the spelling, case and grammar rules for the **PAGE CONTENT** language.\n- Cover various aspects of a topic (benefits, risks, types, comparisons).\n- Include relevant numerical data.\n- Address common questions and misconceptions.\n- Provide practical advice and recommendations.\n- Don't use \"conclusion\" or \"summary\" .\n</language>\n\n<seo_nlp_optimization>\nEnsure the content aligns with SEO best practices and NLP techniques to enhance readability and search engine visibility. Highlight the most relevant information at the beginning of each section.\n</seo_nlp_optimization>\n\nStart directly with the information that needs to be added to be featured in AI Overviews. Nothing more, nothing less."}, {"type": "AIMessagePromptTemplate", "message": "=CONTENT TO ADD:\n"}]}, "batching": {}}, "id": "cc15d895-**************-97c5e7f84697", "name": "Generate SEO Recommendations", "type": "@n8n/n8n-nodes-langchain.chainLlm", "position": [2448, 1040], "notesInFlow": true, "typeVersion": 1.7, "notes": "Create Ai Overviews based guidelines "}, {"parameters": {"content": "## Guidelines flow\nThis flow generates guidelines and stores it in Google Sheets", "height": 680, "width": 2920}, "id": "7443adb0-440c-4223-8362-55641f0afa21", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [368, 832], "typeVersion": 1}, {"parameters": {"assignments": {"assignments": [{"id": "c5ac7498-8037-4daf-8964-c41a9e2c0ddd", "name": "htmlContent", "type": "string", "value": "={{ $json.body.aiOverview.htmlContent }}"}, {"id": "96170e13-fed1-4f4d-a993-c1487d254b2c", "name": "sources", "type": "array", "value": "={{ $json.body.sources }}"}, {"id": "79f421d4-3da7-4817-9f38-e26c49b8bd47", "name": "extractedAt", "type": "string", "value": "={{ $json.body.metadata.extractedAt.toDateTime()}}"}, {"id": "d7129419-db99-40e7-96d2-7893f9474182", "name": "searchQuery", "type": "string", "value": "={{ $json.body.searchQuery }}"}]}, "options": {}}, "id": "5607f7b6-c6b2-4325-95ae-1d749959946b", "name": "Extract Webhook Data", "type": "n8n-nodes-base.set", "position": [672, 368], "typeVersion": 3.4, "notes": "get only necessary data from webhook"}, {"parameters": {"html": "={{ $json.data }}", "options": {}}, "id": "93567700-0a9e-4a19-934e-ee6d98eb9ba6", "name": "Convert Page Content to Markdown", "type": "n8n-nodes-base.markdown", "position": [2016, 944], "notesInFlow": true, "typeVersion": 1, "notes": "convert html to markdown"}], "pinData": {}, "connections": {"Wait": {"main": [[{"node": "Process URLs in Batches", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "myURL Exists", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Generate SEO Recommendations", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Extract Webhook Data", "type": "main", "index": 0}]]}, "myURL Exists": {"main": [[{"node": "Process URLs in Batches", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get URLs to Analyze", "type": "main", "index": 0}]]}, "Fetch Page Content": {"main": [[{"node": "Convert Page Content to Markdown", "type": "main", "index": 0}, {"node": "Extract Page Title and H1", "type": "main", "index": 0}]]}, "Get URLs to Analyze": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Extract Webhook Data": {"main": [[{"node": "Convert AI Overview to Markdown", "type": "main", "index": 0}]]}, "Gemini 2.5 Pro Model": {"ai_languageModel": [[{"node": "Generate SEO Recommendations", "type": "ai_languageModel", "index": 0}]]}, "Get Current URL Data": {"main": [[{"node": "Fetch Page Content", "type": "main", "index": 0}]]}, "Process URLs in Batches": {"main": [[], [{"node": "Get Current URL Data", "type": "main", "index": 0}]]}, "Extract Page Title and H1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Generate SEO Recommendations": {"main": [[{"node": "Save SEO Guidelines to Sheets", "type": "main", "index": 0}]]}, "Save SEO Guidelines to Sheets": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Convert AI Overview to Markdown": {"main": [[{"node": "Save AI Overview to Sheets", "type": "main", "index": 0}]]}, "Convert Page Content to Markdown": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "When clicking 'Execute workflow'": {"main": [[{"node": "Get URLs to Analyze", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "4822", "instanceId": "5b7c7d95a6911543dd86f4042fdad44a0ec5b62bb2578dc8cba0209e92f2ba4c"}, "tags": []}