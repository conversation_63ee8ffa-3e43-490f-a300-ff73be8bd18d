{"name": "Analyze Reddit Posts with AI to Identify Business Opportunities", "nodes": [{"parameters": {}, "id": "52bdf7eb-ee1a-43c5-a0ad-199283003892", "name": "When clicking ‘Test workflow’", "type": "n8n-nodes-base.manualTrigger", "position": [480, 480], "typeVersion": 1}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "id": "e9a000b6-2f35-4928-a8d8-aa2d8cc27513", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [1520, 368], "typeVersion": 1.2}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "id": "cd38a8b6-1369-4209-a80e-9e9949df49c0", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2560, 48], "typeVersion": 1.2}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "id": "4749ca62-6061-4dc0-8f1a-b0e995bb3d0f", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "position": [2512, 912], "typeVersion": 1.2}, {"parameters": {"inputText": "={{ $json.postcontent }}", "options": {}}, "id": "ed68c267-9716-4930-b210-d1f1ae89d8c8", "name": "Post Sentiment Analysis", "type": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "position": [2608, 720], "typeVersion": 1}, {"parameters": {"resource": "draft", "subject": "Positive Post", "message": "={{ $json.postcontent }}", "options": {}}, "id": "2e651e62-00dd-4f0d-a8bc-ce4d8b9fa1d7", "name": "Positive Posts Draft", "type": "n8n-nodes-base.gmail", "position": [3136, 560], "webhookId": "f9dabe4c-9c74-4486-932a-606ea4bb830f", "typeVersion": 2.1}, {"parameters": {"resource": "draft", "subject": "Neutral Post", "message": "={{ $json.postcontent }}", "options": {}}, "id": "29d478d2-43d4-467a-89c2-8c97ea6e245c", "name": "Neutral  Posts Draft", "type": "n8n-nodes-base.gmail", "position": [3152, 752], "webhookId": "f9dabe4c-9c74-4486-932a-606ea4bb830f", "typeVersion": 2.1}, {"parameters": {"resource": "draft", "subject": "Negative Post", "message": "={{ $json.postcontent }}", "options": {}}, "id": "c289805f-5246-4f3a-9052-48c426da8ce0", "name": "Negative  Posts Draft", "type": "n8n-nodes-base.gmail", "position": [3152, 960], "webhookId": "f9dabe4c-9c74-4486-932a-606ea4bb830f", "typeVersion": 2.1}, {"parameters": {"modelId": {"__rl": true, "mode": "list", "value": "gpt-4o-mini", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Based on the following Reddit post, suggest a business idea or service that I could create to help this problem for this business and other with similar needs.\n\nReddit post:  \"{{ $json.postcontent }}\"\n\nProvide a concise description of a business idea or service that would adress this issue effectively for mutiple businesses facing similar challenges.\n"}]}, "options": {}}, "id": "00d17970-3195-4290-bb02-9956f31ecc8f", "name": "Find Proper Solutions", "type": "@n8n/n8n-nodes-langchain.openAi", "position": [2720, 80], "typeVersion": 1.8}, {"parameters": {"options": {}}, "id": "9bcbd874-5eed-47ce-9714-1aec71537fe2", "name": "Post Summarization", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "position": [2640, -160], "typeVersion": 2}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {}}, "id": "60991de9-ad29-484c-9233-966cc1980a03", "name": "Merge <PERSON>", "type": "n8n-nodes-base.merge", "position": [1792, 432], "typeVersion": 3}, {"parameters": {"operation": "append", "documentId": {"__rl": true, "mode": "id", "value": "1C8grVByPo3osYiV5X1pWhEUR9NhBXJGXBE75wC5o6rE"}, "sheetName": {"__rl": true, "mode": "list", "value": "gid=0", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1C8grVByPo3osYiV5X1pWhEUR9NhBXJGXBE75wC5o6rE/edit#gid=0", "cachedResultName": "sheet1"}, "columns": {"value": {"Upvotes": "={{ $json.upvotes }}", "Post_url": "={{ $json.url }}", "Post_date": "={{ $json.date }}", "Post_summary": "={{ $json.response.text }}", "Post_solution": "={{ $json.message.content }}", "Subreddit_size": "={{ $json.subreddit_subscribers }}"}, "schema": [{"id": "Subreddit", "type": "string", "display": true, "removed": true, "required": false, "displayName": "Subreddit", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Subreddit_size", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Subreddit_size", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Post_date", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Post_date", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Upvotes", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Upvotes", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Post_url", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Post_url", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Post_summary", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Post_summary", "defaultMatch": false, "canBeUsedToMatch": true}, {"id": "Post_solution", "type": "string", "display": true, "removed": false, "required": false, "displayName": "Post_solution", "defaultMatch": false, "canBeUsedToMatch": true}], "mappingMode": "defineBelow", "matchingColumns": ["test"], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "id": "f78fbea9-5f7f-4a88-bde1-7c3f01613892", "name": "Output The Results", "type": "n8n-nodes-base.googleSheets", "position": [3392, -144], "typeVersion": 4.5}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 3, "options": {}}, "id": "a0259ed2-0615-4e92-9e7e-cbff8c5bc0ce", "name": "Merge 3 Inputs", "type": "n8n-nodes-base.merge", "position": [3216, 80], "typeVersion": 3}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "0823d10a-ad54-4d82-bcea-9dd236e97857", "operator": {"type": "number", "operation": "gt"}, "leftValue": "={{ $json.ups }}", "rightValue": 2}, {"id": "bb8187aa-f0f1-4999-8d4b-bdc9abba0618", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}, "leftValue": "={{ $json.selftext }}", "rightValue": ""}, {"id": "539f0f5c-025a-4f82-9b3a-2ef1ad3a2d96", "operator": {"type": "dateTime", "operation": "after"}, "leftValue": "={{ DateTime.fromSeconds($json.created).toISO() }}", "rightValue": "={{ $today.minus(180,'days').toISO() }}"}]}, "options": {}}, "id": "b7326fd0-5379-42ac-b355-ef6c3c1790f9", "name": "Filter Posts By Features", "type": "n8n-nodes-base.if", "position": [896, 480], "typeVersion": 2.2}, {"parameters": {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "d5d38c01-3a88-4767-b488-d9c04145bb8f", "operator": {"name": "filter.operator.equals", "type": "string", "operation": "equals"}, "leftValue": "={{ $json.output }}", "rightValue": "yes"}]}, "options": {}}, "id": "11c45f28-97c0-4087-975c-651f27438956", "name": "Filter Posts By Content", "type": "n8n-nodes-base.if", "position": [2048, 448], "typeVersion": 2.2}, {"parameters": {"assignments": {"assignments": [{"id": "e5082ecc-3add-474e-bdb5-b8ad64729930", "name": "upvotes", "type": "string", "value": "={{ $json.ups }}"}, {"id": "a92b5859-fbcc-40c2-95e0-452b12530d98", "name": "subreddit_subscribers", "type": "number", "value": "={{ $json.subreddit_subscribers }}"}, {"id": "a846e21c-6cff-4521-9e0c-a32fa1305376", "name": "postcontent", "type": "string", "value": "={{ $json.selftext }}"}, {"id": "b8045389-684d-4872-9e32-9a6b5511eb2b", "name": "url", "type": "string", "value": "={{ $json.url }}"}, {"id": "f182fedc-1b09-40fe-aeb5-2473263da442", "name": "date", "type": "string", "value": "={{ DateTime.fromSeconds($json.created).toISO() }}"}]}, "options": {}}, "id": "efede239-eff5-4e38-b40d-3cefea040644", "name": "Select Key Fields", "type": "n8n-nodes-base.set", "position": [1136, 464], "typeVersion": 3.4}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "Decide whether this reddit post is describing a business-related problem or a need for a solution. The post  should mention a specific challenge \n or requirement that a business is trying to address.\nReddit post:  {{ $json.postcontent }}\nIs this post about a business problem or need for a solution ? Output only yes or no", "options": {}}, "id": "1a99fdaa-6857-4210-a695-71ce531c1fa0", "name": "Analysis Content  By AI", "type": "@n8n/n8n-nodes-langchain.agent", "position": [1408, 192], "typeVersion": 1.7}, {"parameters": {"operation": "search", "subreddit": "=smallbusiness", "keyword": "looking for a solution", "limit": 20, "additionalFields": {"sort": "hot"}}, "id": "72b30080-e1f2-48b4-b816-ff43542cc6f1", "name": "Get Posts", "type": "n8n-nodes-base.reddit", "position": [688, 480], "typeVersion": 1}, {"parameters": {"content": "# Data Collection\n## Retrieves recent popular posts from specified Reddit communities\n## Filters content by engagement metrics and keywords", "height": 440, "width": 880}, "id": "c4ecb9ec-4895-4b41-ba7e-185e3769ce41", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [368, 240], "typeVersion": 1}, {"parameters": {"content": "# Post Sentiment Analysis\n## ", "height": 660, "width": 1020}, "id": "49735ff9-7f15-4050-9c46-6c13666479bd", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [2432, 512], "typeVersion": 1}, {"parameters": {"content": "# Analysis Content\n## Emerging market needs\n## Underserved customer demands", "height": 680, "width": 820, "color": 4}, "id": "17048a9e-6080-406e-a4e7-5d57406576e1", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [1376, -48], "typeVersion": 1}, {"parameters": {"content": "# Insight Generation And Output \n## Generates executive summaries of key opportunities\n## Consolidates findings in Google Sheets", "height": 640, "width": 1220, "color": 6}, "id": "5a412abc-3866-44ba-9ab0-8cc0a3e012f2", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [2400, -368], "typeVersion": 1}], "pinData": {}, "connections": {"Get Posts": {"main": [[{"node": "Filter Posts By Features", "type": "main", "index": 0}]]}, "Merge Input": {"main": [[{"node": "Filter Posts By Content", "type": "main", "index": 0}]]}, "Merge 3 Inputs": {"main": [[{"node": "Output The Results", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Analysis Content  By AI", "type": "ai_languageModel", "index": 0}]]}, "Select Key Fields": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 1}, {"node": "Analysis Content  By AI", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Post Summarization", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Post Sentiment Analysis", "type": "ai_languageModel", "index": 0}]]}, "Output The Results": {"main": [[]]}, "Post Summarization": {"main": [[{"node": "Merge 3 Inputs", "type": "main", "index": 0}]]}, "Find Proper Solutions": {"main": [[{"node": "Merge 3 Inputs", "type": "main", "index": 1}]]}, "Analysis Content  By AI": {"main": [[{"node": "Merge <PERSON>", "type": "main", "index": 0}]]}, "Filter Posts By Content": {"main": [[{"node": "Post Summarization", "type": "main", "index": 0}, {"node": "Find Proper Solutions", "type": "main", "index": 0}, {"node": "Merge 3 Inputs", "type": "main", "index": 2}, {"node": "Post Sentiment Analysis", "type": "main", "index": 0}]]}, "Post Sentiment Analysis": {"main": [[{"node": "Positive Posts Draft", "type": "main", "index": 0}], [{"node": "Neutral  Posts Draft", "type": "main", "index": 0}], [{"node": "Negative  Posts Draft", "type": "main", "index": 0}]]}, "Filter Posts By Features": {"main": [[{"node": "Select Key Fields", "type": "main", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "Get Posts", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {"templateId": "2978", "instanceId": "5b7c7d95a6911543dd86f4042fdad44a0ec5b62bb2578dc8cba0209e92f2ba4c"}, "tags": []}